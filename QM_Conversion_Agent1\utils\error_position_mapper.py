"""
Position-based error mapping utilities for precise statement identification
"""

import re
from typing import List, Tuple, Optional, Dict, Any
from formatting.sql_splitter import split_sql_statements


class UniversalErrorExtractor:
    """Extract position information from any database error message"""
    
    def extract_position_info(self, error_message: str) -> Dict[str, int]:
        """Extract line or position information from error message"""
        position_info = {}
        
        # PostgreSQL line format: "LINE 139:"
        line_match = re.search(r'LINE\s+(\d+):', error_message, re.IGNORECASE)
        if line_match:
            position_info['line'] = int(line_match.group(1))
            return position_info
        
        # Position format: "Position: 17013" or "position 15420"
        pos_match = re.search(r'position[:\s]+(\d+)', error_message, re.IGNORECASE)
        if pos_match:
            position_info['position'] = int(pos_match.group(1))
            return position_info
        
        # Line format: "line: 447" or "line 184"
        line_match2 = re.search(r'line[:\s]+(\d+)', error_message, re.IGNORECASE)
        if line_match2:
            position_info['line'] = int(line_match2.group(1))
            return position_info
        
        return position_info


class PositionTracker:
    """Track actual positions during SQL splitting process"""

    def __init__(self, sql_code: str):
        self.sql_code = sql_code
        self.positions = []  # List of (start_pos, end_pos) for each statement

    def split_with_positions(self) -> List[Tuple[int, int]]:
        """Split SQL and track actual positions of each statement"""
        if not self.sql_code or not self.sql_code.strip():
            return []

        # Use similar logic to sql_splitter but track positions
        statements_with_positions = []
        remaining_code = self.sql_code.strip()

        # Find original offset in full SQL
        original_start = self.sql_code.find(remaining_code)
        if original_start == -1:
            original_start = 0

        # First, try to split on AS or IS keywords
        as_is_pattern = r'\b(AS|IS)\b'
        match = re.search(as_is_pattern, remaining_code, re.IGNORECASE)

        if match and not self._is_in_comment_or_quotes(match.start(), remaining_code):
            # Split before the match
            first_part = remaining_code[:match.start()].strip()
            if first_part:
                stmt_start = original_start
                stmt_end = original_start + match.start() - 1
                statements_with_positions.append((stmt_start, stmt_end))

            # Update remaining code and offset
            remaining_code = remaining_code[match.start():].strip()
            original_start = original_start + match.start()

        # Split remaining code by semicolons
        if remaining_code:
            semicolon_positions = [m.start() for m in re.finditer(';', remaining_code)]

            if not semicolon_positions:
                # No semicolons, add remaining as single statement
                stmt_start = original_start
                stmt_end = original_start + len(remaining_code) - 1
                statements_with_positions.append((stmt_start, stmt_end))
            else:
                last_pos = 0
                for pos in semicolon_positions:
                    if not self._is_in_comment_or_quotes(pos, remaining_code):
                        stmt_start = original_start + last_pos
                        stmt_end = original_start + pos
                        statements_with_positions.append((stmt_start, stmt_end))
                        last_pos = pos + 1

                # Add remaining code after last semicolon
                if last_pos < len(remaining_code):
                    remaining_part = remaining_code[last_pos:].strip()
                    if remaining_part:
                        stmt_start = original_start + last_pos
                        stmt_end = original_start + len(remaining_code) - 1
                        statements_with_positions.append((stmt_start, stmt_end))

        return statements_with_positions

    def _is_in_comment_or_quotes(self, pos: int, code: str) -> bool:
        """Check if position is within comment or quotes"""
        # Simple check for comments and quotes
        before_pos = code[:pos]

        # Check for single quotes
        single_quotes = before_pos.count("'")
        if single_quotes % 2 == 1:  # Odd number means we're inside quotes
            return True

        # Check for comments
        if '--' in before_pos:
            last_comment = before_pos.rfind('--')
            last_newline = before_pos.rfind('\n', 0, last_comment)
            if last_newline < last_comment:  # Comment is on same line
                return True

        return False


class AdvancedPositionMapper:
    """Advanced position mapping with comprehensive statement tracking"""
    
    def __init__(self):
        self.statement_ranges = []  # List of (start_pos, end_pos, start_line, end_line)
        self.position_to_statement = {}  # char_pos -> statement_number
        self.line_to_statement = {}  # line_number -> statement_number
        self.statement_content_hash = {}  # statement_number -> content_hash
        self.original_sql = ""
        self.lines = []
    
    def split_with_comprehensive_mapping(self, sql_code: str) -> Tuple[List[str], 'AdvancedPositionMapper']:
        """Split SQL and create comprehensive position mapping"""
        self.original_sql = sql_code
        self.lines = sql_code.split('\n')
        
        # Use the existing SQL splitter
        statements = split_sql_statements(sql_code)
        
        # Create position mapping
        self._create_position_mapping(sql_code, statements)
        
        return statements, self
    
    def _create_position_mapping(self, sql_code: str, statements: List[str]):
        """Create comprehensive position mapping using sequential tracking"""
        # Re-split the SQL to track actual positions during splitting process
        position_tracker = PositionTracker(sql_code)
        tracked_statements = position_tracker.split_with_positions()

        # Ensure we have matching statement counts
        if len(statements) != len(tracked_statements):
            # Fall back to approximate mapping
            self._create_approximate_mapping(sql_code, statements)
            return

        # Create precise mapping using tracked positions
        for stmt_num, (statement, (stmt_start, stmt_end)) in enumerate(zip(statements, tracked_statements), 1):
            # Calculate line numbers
            lines_before = sql_code[:stmt_start].count('\n')
            lines_in_stmt = statement.count('\n')
            start_line = lines_before + 1
            end_line = start_line + lines_in_stmt

            # Store range
            self.statement_ranges.append((stmt_start, stmt_end, start_line, end_line))

            # Map positions to statement
            for pos in range(stmt_start, stmt_end + 1):
                self.position_to_statement[pos] = stmt_num

            # Map lines to statement
            for line in range(start_line, end_line + 1):
                self.line_to_statement[line] = stmt_num

            # Create content hash for duplicate detection
            content_hash = hash(statement.strip().lower())
            self.statement_content_hash[stmt_num] = content_hash

    def _create_approximate_mapping(self, sql_code: str, statements: List[str]):
        """Fallback approximate position mapping"""
        current_pos = 0

        for stmt_num, statement in enumerate(statements, 1):
            # Estimate statement length
            stmt_length = len(statement.strip())
            stmt_start = current_pos
            stmt_end = current_pos + stmt_length

            # Calculate line numbers
            lines_before = sql_code[:stmt_start].count('\n')
            lines_in_stmt = statement.count('\n')
            start_line = lines_before + 1
            end_line = start_line + lines_in_stmt

            # Store range
            self.statement_ranges.append((stmt_start, stmt_end, start_line, end_line))

            # Map positions to statement (limited range to avoid conflicts)
            for pos in range(stmt_start, min(stmt_end + 1, len(sql_code))):
                self.position_to_statement[pos] = stmt_num

            # Map lines to statement
            for line in range(start_line, end_line + 1):
                self.line_to_statement[line] = stmt_num

            # Create content hash for duplicate detection
            content_hash = hash(statement.strip().lower())
            self.statement_content_hash[stmt_num] = content_hash

            current_pos = stmt_end + 2  # Add buffer for semicolon and whitespace
    
    def find_statement_by_any_position(self, position_info: Dict[str, int]) -> List[Tuple[int, str, int]]:
        """Find statement by line or character position"""
        candidates = []
        
        if 'line' in position_info:
            line_num = position_info['line']
            if line_num in self.line_to_statement:
                stmt_num = self.line_to_statement[line_num]
                candidates.append((stmt_num, 'line', line_num))
        
        if 'position' in position_info:
            char_pos = position_info['position']
            if char_pos in self.position_to_statement:
                stmt_num = self.position_to_statement[char_pos]
                candidates.append((stmt_num, 'position', char_pos))
        
        return candidates
    
    def get_duplicate_statements(self, statement_num: int) -> List[int]:
        """Get all statements with same content hash (duplicates)"""
        if statement_num not in self.statement_content_hash:
            return [statement_num]
        
        target_hash = self.statement_content_hash[statement_num]
        duplicates = []
        
        for stmt_num, content_hash in self.statement_content_hash.items():
            if content_hash == target_hash:
                duplicates.append(stmt_num)
        
        return sorted(duplicates)
    
    def to_dict(self) -> Dict[str, Any]:
        """Serialize to dictionary"""
        return {
            'statement_ranges': self.statement_ranges,
            'position_to_statement': self.position_to_statement,
            'line_to_statement': self.line_to_statement,
            'statement_content_hash': self.statement_content_hash,
            'original_sql': self.original_sql,
            'lines': self.lines
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AdvancedPositionMapper':
        """Deserialize from dictionary - no default values"""
        if not data:
            raise ValueError("Cannot deserialize from empty data")

        mapper = cls()

        # Direct access without defaults - will raise KeyError if missing
        try:
            mapper.statement_ranges = data['statement_ranges']
            mapper.position_to_statement = data['position_to_statement']
            mapper.line_to_statement = data['line_to_statement']
            mapper.statement_content_hash = data['statement_content_hash']
            mapper.original_sql = data['original_sql']
            mapper.lines = data['lines']
        except KeyError as e:
            raise ValueError(f"Missing required key in position mapper data: {e}")

        return mapper


class SmartStatementResolver:
    """Simple position-based statement resolver"""
    
    def __init__(self, position_mapper: AdvancedPositionMapper):
        self.position_mapper = position_mapper
    
    def resolve_statement_by_position(self, error_message: str, iteration_count: int = 1) -> Optional[int]:
        """Simple position-based statement resolution"""

        # Extract position information
        extractor = UniversalErrorExtractor()
        position_info = extractor.extract_position_info(error_message)

        if not position_info:
            return None

        # Get candidate statements
        candidates = self.position_mapper.find_statement_by_any_position(position_info)

        if not candidates:
            return None

        # Handle multiple candidates (duplicates)
        if len(candidates) == 1:
            statement_num = candidates[0][0]
            return statement_num

        # Multiple candidates - use iteration to select different ones
        # Get duplicates for the first candidate
        first_candidate = candidates[0][0]
        duplicates = self.position_mapper.get_duplicate_statements(first_candidate)

        if len(duplicates) > 1:
            # Rotate through duplicates based on iteration
            selected_index = (iteration_count - 1) % len(duplicates)
            selected_statement = duplicates[selected_index]
            return selected_statement

        # No duplicates, just return first candidate
        statement_num = candidates[0][0]
        return statement_num
